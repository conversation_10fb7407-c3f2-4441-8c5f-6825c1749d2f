export function setNoHeadingByLevel(level) {
  Asc.scope.level = level;
  window.Asc.plugin.callCommand(
    function () {
      var introductionStyle = '标准文件_引言一级无标题';
      switch (Asc.scope.level) {
        case 1:
          introductionStyle = '标准文件_引言一级无标题';
          break;
        case 2:
          introductionStyle = '标准文件_引言二级无标题';
          break;
        case 3:
          introductionStyle = '标准文件_引言三级无标题';
          break;
        case 4:
          introductionStyle = '标准文件_引言四级无标题';
          break;
        case 5:
          introductionStyle = '标准文件_引言五级无标题';
          break;
        default:
          introductionStyle = '标准文件_引言一级无标题';
      }

      var chapterStyle = '标准文件_一级无标题';
      switch (Asc.scope.level) {
        case 1:
          chapterStyle = '标准文件_一级无标题';
          break;
        case 2:
          chapterStyle = '标准文件_二级无标题';
          break;
        case 3:
          chapterStyle = '标准文件_三级无标题';
          break;
        case 4:
          chapterStyle = '标准文件_四级无标题';
          break;
        case 5:
          chapterStyle = '标准文件_五级无标题';
          break;
        default:
          chapterStyle = '标准文件_一级无标题';
      }

      var appendixStyle = '标准文件_附录一级无标题';
      switch (Asc.scope.level) {
        case 1:
          appendixStyle = '标准文件_附录一级无标题';
          break;
        case 2:
          appendixStyle = '标准文件_附录二级无标题';
          break;
        case 3:
          appendixStyle = '标准文件_附录三级无标题';
          break;
        case 4:
          appendixStyle = '标准文件_附录四级无标题';
          break;
        case 5:
          appendixStyle = '标准文件_附录五级无标题';
          break;
        default:
          appendixStyle = '标准文件_附录一级无标题';
      }

      let oDocument = Api.GetDocument();
      let allParagraphs = oDocument.GetAllParagraphs();
      let currentStyle = chapterStyle;

      function getCurrentParagraphPosition() {
        var oDocument = Api.GetDocument();
        var currentSentence = oDocument.GetCurrentSentence() || '';
        var searchStr = '^$' + currentSentence;
        oDocument.ReplaceCurrentSentence(searchStr);
        var targetPosition = -1;
        var allParagraphs = oDocument.GetAllParagraphs();
        for (var i = 0; i < allParagraphs.length; i++) {
          var oParagraph = allParagraphs[i];
          var oText = oParagraph.GetText().trim();
          if (oText.includes(searchStr.trim())) {
            targetPosition = i;
            oDocument.ReplaceCurrentSentence(currentSentence);
            break;
          }
        }
        return targetPosition;
      }

      function getPosition() {
        let currentIndex = -1;
        let introductionIndex = -1;
        let standardIndex = -1;
        let appendixIndex = -1;
        var currentSentence = oDocument.GetCurrentSentence() || '';
        var searchStr = '^$' + currentSentence;
        oDocument.ReplaceCurrentSentence(searchStr);

        for (var i = 0; i < allParagraphs.length; i++) {
          let paragraph = allParagraphs[i];
          let text = paragraph.GetText().trim();
          let style = paragraph?.GetStyle()?.GetName();
          if (text.includes(searchStr.trim())) {
            currentIndex = i;
            oDocument.ReplaceCurrentSentence(currentSentence);
          }
          if (text === '引言' && style === '标准文件_前言、引言标题') introductionIndex = i;

          if (style === '标准文件_正文标准名称') standardIndex = i;

          if (style === '标准文件_附录标识' && appendixIndex == -1) appendixIndex = i;
        }

        return { currentIndex, introductionIndex, standardIndex, appendixIndex };
      }

      var { currentIndex, introductionIndex, standardIndex, appendixIndex } = getPosition();

      if (currentIndex == -1) return { error: '未找到当前段落' };

      if (standardIndex == -1) return { error: '未找到正文标题' };

      if (introductionIndex != -1 && introductionIndex < currentIndex && currentIndex < standardIndex) {
        currentStyle = introductionStyle;
      }

      if (appendixIndex != -1 && currentIndex > appendixIndex) {
        currentStyle = appendixStyle;
      }

      try {
        var targetParagraph = allParagraphs[currentIndex];
        var hasStyle = targetParagraph.GetStyle() && targetParagraph.GetStyle().GetName() == currentStyle;
        if (!hasStyle) {
          targetParagraph.SetStyle(oDocument.GetStyle('标准文件_段'));
          let text = targetParagraph.GetText();
          let nParagraph = Api.CreateParagraph();
          nParagraph.AddText(text);
          nParagraph.SetStyle(oDocument.GetStyle(currentStyle));
          nParagraph.SetIndFirstLine(0);
          targetParagraph.InsertParagraph(nParagraph, 'after');
          targetParagraph.Delete();
          getCurrentParagraphPosition();
          return;
        }
      } catch (e) {
        return { error: e.message };
      }
    },
    false,
    true,
    function (result) {
      if (result && result.error) {
        window.Asc.plugin.executeMethod('ShowError', [result.error]);
      }
      window.Asc.plugin.executeMethod('EndAction', ['Block', 'Save to local storage...', '']);
      window.Asc.plugin.executeCommand('close', '');
    }
  );
}
