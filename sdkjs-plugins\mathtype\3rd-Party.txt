This plugin uses code from the following 3rd party projects.


1. MathType is a software application created by Design Science that allows the creation of mathematical notation for inclusion in desktop and web applications. (https://www.wiris.com/en/mathtype/)

2. jQuery - Query is a fast and concise JavaScript Library that simplifies HTML document traversing, event handling, animating, and Ajax interactions for rapid web development. (http://jquery.com/)

License:        MIT License
License File:   jQuery.license